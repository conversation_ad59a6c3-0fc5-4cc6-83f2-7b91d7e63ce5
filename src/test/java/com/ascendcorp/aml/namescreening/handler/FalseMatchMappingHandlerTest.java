package com.ascendcorp.aml.namescreening.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

import com.ascendcorp.aml.namescreening.config.ApplicationProperties;
import com.ascendcorp.aml.namescreening.config.es.ElasticsearchProperties;
import com.ascendcorp.aml.namescreening.entity.FalseMatchEntity;
import com.ascendcorp.aml.namescreening.entity.MerchantFalseMatchEntity;
import com.ascendcorp.aml.namescreening.repository.FalseMatchRepository;
import com.ascendcorp.aml.namescreening.repository.MerchantFalseMatchRepository;
import com.ascendcorp.aml.namescreening.request.ExtraUserFalseMatchRequest;
import com.ascendcorp.aml.namescreening.request.MerchantRescoringRequest;
import com.ascendcorp.aml.namescreening.request.RescoringRequest;
import com.ascendcorp.aml.namescreening.request.UboRescoringRequest;
import com.ascendcorp.aml.namescreening.util.CommonTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;

import org.opensearch.action.index.IndexResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.core.rest.RestStatus;
import org.opensearch.index.reindex.BulkByScrollResponse;
import org.opensearch.index.reindex.BulkByScrollTask.Status;
import org.opensearch.index.reindex.DeleteByQueryRequest;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.cache.caffeine.CaffeineCache;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.StringQuery;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class FalseMatchMappingHandlerTest {

  @Mock
  private FalseMatchRepository falseMatchRepository;

  @Mock
  private MerchantFalseMatchRepository merchantFalseMatchRepository;

  private ElasticsearchProperties elasticsearchProperties;

  @Mock
  private EsSearchQueryBuilder esSearchQueryBuilder;

  private FalseMatchMappingHandler falseMatchMappingHandler;

  private final String INCIDENT_ID = "incidentId";

  private final String PROFILE_ID = "profileId";

  private final String COUNTRY_CODE = "countryCode";

  private final String REF_ID = "refId";

  private final String EXTRA_USER_ID = "extraUserId";

  private final List<String> ENTITIES_ID = Arrays.asList("DJ02371","DJ02374");

  @BeforeEach
  void setup() {
    ApplicationProperties applicationProperties = new ApplicationProperties();
    applicationProperties.setEnvironment("_DEV");
    elasticsearchProperties = new ElasticsearchProperties(applicationProperties);
    elasticsearchProperties.setMaxSize(10);
    falseMatchMappingHandler = new FalseMatchMappingHandler(falseMatchRepository,
        merchantFalseMatchRepository, elasticsearchProperties,
        esSearchQueryBuilder);
  }

  @Test
  void test__saveToEls__shouldReturnSuccess() {
    RescoringRequest request = CommonTestUtils.buildRescoringRequest();
    BulkByScrollResponse delResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(delResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(1L);
    when(falseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class)))
        .thenReturn(delResponse);
    IndexResponse insertResponse = mock(IndexResponse.class);
    when(insertResponse.status()).thenReturn(RestStatus.CREATED);
    when(falseMatchRepository.saveToEls(any(FalseMatchEntity.class), anyString()))
        .thenReturn(insertResponse);

    falseMatchMappingHandler.saveToEls(request);

    verify(falseMatchRepository, times(1))
        .deleteFromEls(any(DeleteByQueryRequest.class));
    verify(falseMatchRepository, times(1))
        .saveToEls(any(FalseMatchEntity.class), anyString());
  }

  @Test
  void test__saveToEls__whenInsertFail__shouldReturnSuccess() {
    RescoringRequest request = CommonTestUtils.buildRescoringRequest();
    BulkByScrollResponse delResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(delResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(1L);
    when(falseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class)))
        .thenReturn(delResponse);
    IndexResponse insertResponse = mock(IndexResponse.class);
    when(insertResponse.status()).thenReturn(RestStatus.NOT_MODIFIED);
    when(falseMatchRepository.saveToEls(any(FalseMatchEntity.class), anyString()))
        .thenReturn(insertResponse);

    falseMatchMappingHandler.saveToEls(request);

    verify(falseMatchRepository, times(1))
        .deleteFromEls(any(DeleteByQueryRequest.class));
    verify(falseMatchRepository, times(1))
        .saveToEls(any(FalseMatchEntity.class), anyString());
  }


  @Test
  void test__getListFalseMatchEntityIds__shouldReturnSuccess() {
    FalseMatchEntity entity = new FalseMatchEntity();
    entity.setEntities("e1,e2,e3");
    List<FalseMatchEntity> falseMatchEntities = Collections.singletonList(entity);
    when(esSearchQueryBuilder.buildFalseMatchSearchRequest(anyString(), anyString(), anyString()))
        .thenReturn(buildStringQuery());
    when(falseMatchRepository.findBySearchRequest(any(StringQuery.class), anyString()))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListFalseMatchEntityIds(PROFILE_ID, COUNTRY_CODE);

    Assertions.assertEquals(expectedResult, actualResult);
  }


  @Test
  void test__getListFalseMatchEntityIds__whenDuplicate__shouldReturnSuccess() {
    FalseMatchEntity entity1 = new FalseMatchEntity();
    entity1.setEntities("e1,e2,e3");
    FalseMatchEntity entity2 = new FalseMatchEntity();
    entity2.setEntities("e2,e3");
    List<FalseMatchEntity> falseMatchEntities = Arrays.asList(entity1, entity2);
    when(esSearchQueryBuilder.buildFalseMatchSearchRequest(anyString(), anyString(), anyString()))
        .thenReturn(buildStringQuery());
    when(falseMatchRepository.findBySearchRequest(any(StringQuery.class), anyString()))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListFalseMatchEntityIds(PROFILE_ID, COUNTRY_CODE);

    Assertions.assertEquals(expectedResult, actualResult);

  }

  @Test
  void test__getListFalseMatchEntityIds__byIncidentId__shouldReturnSuccess() {
    FalseMatchEntity entity = new FalseMatchEntity();
    entity.setEntities("e1,e2,e3");
    List<FalseMatchEntity> falseMatchEntities = Collections.singletonList(entity);
    when(falseMatchRepository.findBySearchRequest(any(SearchRequest.class)))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListFalseMatchEntityIds(INCIDENT_ID);

    Assertions.assertEquals(expectedResult, actualResult);
  }

  @Test
  void givenMerchantRescoringRequest__saveMerchantFalseMatchToEls__shouldReturnSuccess() {
    MerchantRescoringRequest request = CommonTestUtils.buildMerchantRescoringRequest();
    request.setExtraUserFalseMatch(Collections.singletonList(initExtraUserFalseMatchRequest()));
    BulkByScrollResponse delResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(delResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(1L);
    when(merchantFalseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class)))
        .thenReturn(delResponse);
    IndexResponse indexResponse = mock(IndexResponse.class);
    when(indexResponse.status()).thenReturn(RestStatus.CREATED);
    when(merchantFalseMatchRepository.saveToEls(any(), anyString()))
        .thenReturn(indexResponse);

    falseMatchMappingHandler.saveMerchantFalseMatchToEls(request);

    verify(merchantFalseMatchRepository, times(1))
        .deleteFromEls(any(DeleteByQueryRequest.class));
    verify(merchantFalseMatchRepository, times(2))
        .saveToEls(any(), anyString());
  }

  @Test
  void givenMerchantRescoringRequest__saveMerchantFalseMatchToEls__whenInsertFail__shouldReturnSuccess() {
    MerchantRescoringRequest request = CommonTestUtils.buildMerchantRescoringRequest();
    BulkByScrollResponse delResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(delResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(1L);
    when(merchantFalseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class)))
        .thenReturn(delResponse);
    IndexResponse indexResponse = mock(IndexResponse.class);
    when(indexResponse.status()).thenReturn(RestStatus.NOT_MODIFIED);
    when(merchantFalseMatchRepository.saveToEls(any(), anyString()))
        .thenReturn(indexResponse);

    falseMatchMappingHandler.saveMerchantFalseMatchToEls(request);

    verify(merchantFalseMatchRepository, times(1))
        .deleteFromEls(any(DeleteByQueryRequest.class));
    verify(merchantFalseMatchRepository, times(1))
        .saveToEls(any(), anyString());
  }

  @Test
  void givenFalseMatchEntity__whenGetListMerchantFalseMatchEntityIds__shouldReturnSuccess() {
    MerchantFalseMatchEntity entity = new MerchantFalseMatchEntity();
    entity.setEntities("e1,e2,e3");
    List<MerchantFalseMatchEntity> falseMatchEntities = Collections.singletonList(entity);
    when(merchantFalseMatchRepository.findBySearchRequest(any(SearchRequest.class)))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListMerchantFalseMatchEntityIds(PROFILE_ID, COUNTRY_CODE);

    Assertions.assertEquals(expectedResult, actualResult);
  }


  @Test
  void givenFalseMatchEntityWithDuplicateEntityId__whenGetListMerchantFalseMatchEntityIds__shouldReturnSuccess() {
    MerchantFalseMatchEntity entity1 = new MerchantFalseMatchEntity();
    entity1.setEntities("e1,e2,e3");
    MerchantFalseMatchEntity entity2 = new MerchantFalseMatchEntity();
    entity2.setEntities("e2,e3");
    List<MerchantFalseMatchEntity> falseMatchEntities = Arrays.asList(entity1, entity2);
    when(merchantFalseMatchRepository.findBySearchRequest(any(SearchRequest.class)))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListMerchantFalseMatchEntityIds(PROFILE_ID, COUNTRY_CODE);

    Assertions.assertEquals(expectedResult, actualResult);

  }

  @Test
  void givenFalseMatchEntity__getListMerchantFalseMatchEntityIdsByIncidentId__shouldReturnSuccess() {
    MerchantFalseMatchEntity entity = new MerchantFalseMatchEntity();
    entity.setEntities("e1,e2,e3");
    List<MerchantFalseMatchEntity> falseMatchEntities = Collections.singletonList(entity);
    when(merchantFalseMatchRepository.findBySearchRequest(any(SearchRequest.class)))
        .thenReturn(falseMatchEntities);
    List<String> expectedResult = Arrays.asList("e1", "e2", "e3");

    List<String> actualResult = falseMatchMappingHandler
        .getListMerchantFalseMatchEntityIds(INCIDENT_ID);

    Assertions.assertEquals(expectedResult, actualResult);
  }

  @Test
  void giveUboRescoringRequest_whenSaveUboFalseMatchToEls_thenSaveSuccessToELS(){
    UboRescoringRequest request = new UboRescoringRequest();
    request.setCountryCode(COUNTRY_CODE);
    request.setIncidentId(INCIDENT_ID);
    request.setProfileId(PROFILE_ID);
    request.setReferenceId(REF_ID);
    request.setFalseMatchEntities(ENTITIES_ID);
    request.setExtraUserFalseMatch(Collections.singletonList(initExtraUserFalseMatchRequest()));

    BulkByScrollResponse bulkByScrollResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(bulkByScrollResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(5L);

    IndexResponse indexResponse = mock(IndexResponse.class);
    when(indexResponse.status()).thenReturn(RestStatus.CREATED);

    when(falseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class))).thenReturn(bulkByScrollResponse);
    when(falseMatchRepository.saveToEls(any(),anyString())).thenReturn(indexResponse);

    falseMatchMappingHandler.saveUboFalseMatchToEls(request);
    verify(falseMatchRepository,times(1)).deleteFromEls(any(DeleteByQueryRequest.class));
    verify(falseMatchRepository,times(3)).saveToEls(any(),anyString());
  }

  @Test
  void giveUboRescoringRequest_whenSaveUboFalseMatchToEls_thenSavedFailToELS(){
    UboRescoringRequest request = new UboRescoringRequest();
    request.setCountryCode(COUNTRY_CODE);
    request.setIncidentId(INCIDENT_ID);
    request.setProfileId(PROFILE_ID);
    request.setReferenceId(REF_ID);
    request.setFalseMatchEntities(ENTITIES_ID);
    request.setExtraUserFalseMatch(Collections.singletonList(initExtraUserFalseMatchRequest()));

    BulkByScrollResponse bulkByScrollResponse = mock(BulkByScrollResponse.class);
    Status status = mock(Status.class);
    when(bulkByScrollResponse.getStatus()).thenReturn(status);
    when(status.getDeleted()).thenReturn(5L);

    IndexResponse indexResponse = mock(IndexResponse.class);
    when(indexResponse.status()).thenReturn(RestStatus.NOT_MODIFIED);

    when(falseMatchRepository.deleteFromEls(any(DeleteByQueryRequest.class))).thenReturn(bulkByScrollResponse);
    when(falseMatchRepository.saveToEls(any(),anyString())).thenReturn(indexResponse);

    falseMatchMappingHandler.saveUboFalseMatchToEls(request);
    verify(falseMatchRepository,times(1)).deleteFromEls(any(DeleteByQueryRequest.class));
    verify(falseMatchRepository,times(3)).saveToEls(any(),anyString());
  }

  @Test
  void giveListProfileIds_whenGetListFalseMatchEntityIds_thenReturnSuccess(){
    List<String> profileIds = Arrays.asList(PROFILE_ID, "37291");

    FalseMatchEntity falseMatchEntity_1 = new FalseMatchEntity();
    falseMatchEntity_1.setEntities("DJ2810");
    falseMatchEntity_1.setCountryCode("VNM");
    falseMatchEntity_1.setIncidentId(INCIDENT_ID);
    falseMatchEntity_1.setProfileId(PROFILE_ID);

    FalseMatchEntity falseMatchEntity_2 = new FalseMatchEntity();
    falseMatchEntity_2.setEntities("DJ2810");
    falseMatchEntity_2.setCountryCode("VNM");
    falseMatchEntity_2.setIncidentId(INCIDENT_ID);
    falseMatchEntity_2.setProfileId(PROFILE_ID);

    when(falseMatchRepository.findBySearchRequest(any(SearchRequest.class))).thenReturn(Arrays.asList(falseMatchEntity_1,falseMatchEntity_2));

    falseMatchMappingHandler.getListFalseMatchEntityIds(profileIds,"VNM");
    verify(falseMatchRepository,times(1)).findBySearchRequest(any(SearchRequest.class));
  }

  @Test
  void giveListMerchantProfileIds_whenGetListMerchantFalseMatchEntity_thenReturnSuccess(){
    List<String> merchantProfileIds = Arrays.asList(PROFILE_ID, "37291");

    MerchantFalseMatchEntity falseMatchEntity_1 = new MerchantFalseMatchEntity();
    falseMatchEntity_1.setEntities("DJ2810");
    falseMatchEntity_1.setCountryCode("VNM");
    falseMatchEntity_1.setIncidentId(INCIDENT_ID);
    falseMatchEntity_1.setMerchantProfileId(PROFILE_ID);

    MerchantFalseMatchEntity falseMatchEntity_2 = new MerchantFalseMatchEntity();
    falseMatchEntity_2.setEntities("DJ2810");
    falseMatchEntity_2.setCountryCode("VNM");
    falseMatchEntity_2.setIncidentId(INCIDENT_ID);
    falseMatchEntity_2.setMerchantProfileId(PROFILE_ID);

    when(merchantFalseMatchRepository.findBySearchRequest(any(SearchRequest.class))).thenReturn(Arrays.asList(falseMatchEntity_1,falseMatchEntity_2));

    falseMatchMappingHandler.getListMerchantFalseMatchEntity(merchantProfileIds,"VNM");
    verify(merchantFalseMatchRepository,times(1)).findBySearchRequest(any(SearchRequest.class));
  }

  private ExtraUserFalseMatchRequest initExtraUserFalseMatchRequest(){
    ExtraUserFalseMatchRequest request = new ExtraUserFalseMatchRequest();
    request.setExtraUserId(EXTRA_USER_ID);
    request.setFalseMatchEntities(ENTITIES_ID);

    return request;
  }

  private StringQuery buildStringQuery() {
    StringQuery stringQuery = new StringQuery("QUERY");
    stringQuery.setPageable(PageRequest.of(0, 10));
    return stringQuery;
  }

  @Test
  void test__initializeFalseMatchCache__shouldPopulateCache() throws InterruptedException {
    // Arrange
    List<FalseMatchEntity> falseMatchEntities = new ArrayList<>();
    FalseMatchEntity entity1 = new FalseMatchEntity();
    entity1.setProfileId("profile1");
    entity1.setCountryCode("TH");
    entity1.setEntities("entity1,entity2");

    FalseMatchEntity entity2 = new FalseMatchEntity();
    entity2.setProfileId("profile2");
    entity2.setCountryCode("VN");
    entity2.setEntities("entity3,entity4");

    falseMatchEntities.add(entity1);
    falseMatchEntities.add(entity2);

    when(falseMatchRepository.findByScrollRequest(any(StringQuery.class), anyString(), anyLong()))
        .thenReturn(falseMatchEntities);

    // Act
    falseMatchMappingHandler.initializeFalseMatchCache();

    // Assert
    verify(falseMatchRepository).findByScrollRequest(any(StringQuery.class),
        eq(elasticsearchProperties.getMappingFalseMatchIndexName()), eq(60000L));

    // Test cache hit
    List<String> result1 = falseMatchMappingHandler.getListFalseMatchEntityIds("profile1", "TH");
    Assertions.assertEquals(Arrays.asList("entity1", "entity2"), result1);

    List<String> result2 = falseMatchMappingHandler.getListFalseMatchEntityIds("profile2", "VN");
    Assertions.assertEquals(Arrays.asList("entity3", "entity4"), result2);
  }

  @Test
  void test__getListFalseMatchEntityIds__whenCacheNotInitialized__shouldQueryElasticsearch() {
    // Arrange
    StringQuery mockQuery = mock(StringQuery.class);
    when(esSearchQueryBuilder.buildFalseMatchSearchRequest(anyString(), anyString(), anyString()))
        .thenReturn(mockQuery);

    FalseMatchEntity entity = new FalseMatchEntity();
    entity.setEntities("entity5,entity6");
    List<FalseMatchEntity> falseMatchEntities = Collections.singletonList(entity);

    when(falseMatchRepository.findBySearchRequest(any(StringQuery.class), anyString()))
        .thenReturn(falseMatchEntities);

    // Set cache as not initialized
    ReflectionTestUtils.setField(falseMatchMappingHandler, "isCacheFalseMatchInitialized", false);

    // Act
    List<String> result = falseMatchMappingHandler.getListFalseMatchEntityIds("profile3", "SG");

    // Assert
    verify(falseMatchRepository).findBySearchRequest(any(StringQuery.class),
        eq(elasticsearchProperties.getMappingFalseMatchIndexName()));

    Assertions.assertEquals(Arrays.asList("entity5", "entity6"), result);
  }

  @Test
  void test__initializeFalseMatchCache__whenExceptionOccurs__shouldRetryAndLogError() throws InterruptedException {
    // Arrange
    when(falseMatchRepository.findByScrollRequest(any(StringQuery.class), anyString(), anyLong()))
        .thenThrow(new RuntimeException("Test exception"));

    // Act
    falseMatchMappingHandler.initializeFalseMatchCache();

    // Assert
    verify(falseMatchRepository, times(3)).findByScrollRequest(any(StringQuery.class),
        eq(elasticsearchProperties.getMappingFalseMatchIndexName()), eq(60000L));

    // Verify cache is not initialized
    boolean isCacheInitialized = (boolean) ReflectionTestUtils.getField(falseMatchMappingHandler, "isCacheFalseMatchInitialized");
    Assertions.assertFalse(isCacheInitialized);
  }

  @Test
  void test__getListFalseMatchEntityIds__whenProfileIdOrCountryCodeBlank__shouldReturnEmptyList() {
    // Test with blank profileId
    List<String> result1 = falseMatchMappingHandler.getListFalseMatchEntityIds("", "TH");
    Assertions.assertTrue(result1.isEmpty());

    // Test with blank countryCode
    List<String> result2 = falseMatchMappingHandler.getListFalseMatchEntityIds("profile1", "");
    Assertions.assertTrue(result2.isEmpty());

    // Test with both blank
    List<String> result3 = falseMatchMappingHandler.getListFalseMatchEntityIds("", "");
    Assertions.assertTrue(result3.isEmpty());

    // Test with null profileId
    List<String> result4 = falseMatchMappingHandler.getListFalseMatchEntityIds((String) null, "TH");
    Assertions.assertTrue(result4.isEmpty());

    // Test with null countryCode
    List<String> result5 = falseMatchMappingHandler.getListFalseMatchEntityIds("profile1", null);
    Assertions.assertTrue(result5.isEmpty());

    // Verify that no repository calls were made
    verify(falseMatchRepository, never()).findBySearchRequest(any(StringQuery.class), anyString());
    verify(esSearchQueryBuilder, never()).buildFalseMatchSearchRequest(anyString(), anyString(), anyString());
  }

  @Test
  void test__getListMerchantFalseMatchEntityIds__whenMerchantProfileIdOrCountryCodeBlank__shouldReturnEmptyList() {
    // Test with blank merchantProfileId
    List<String> result1 = falseMatchMappingHandler.getListMerchantFalseMatchEntityIds("", "TH");
    Assertions.assertTrue(result1.isEmpty());

    // Test with blank countryCode
    List<String> result2 = falseMatchMappingHandler.getListMerchantFalseMatchEntityIds("merchant1", "");
    Assertions.assertTrue(result2.isEmpty());

    // Test with both blank
    List<String> result3 = falseMatchMappingHandler.getListMerchantFalseMatchEntityIds("", "");
    Assertions.assertTrue(result3.isEmpty());

    // Test with null merchantProfileId
    List<String> result4 = falseMatchMappingHandler.getListMerchantFalseMatchEntityIds(null, "TH");
    Assertions.assertTrue(result4.isEmpty());

    // Test with null countryCode
    List<String> result5 = falseMatchMappingHandler.getListMerchantFalseMatchEntityIds("merchant1", null);
    Assertions.assertTrue(result5.isEmpty());

    // Verify that no repository calls were made
    verify(merchantFalseMatchRepository, never()).findBySearchRequest(any(SearchRequest.class));
  }
}
