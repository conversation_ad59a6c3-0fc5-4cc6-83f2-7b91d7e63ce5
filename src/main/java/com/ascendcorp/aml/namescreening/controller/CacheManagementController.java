package com.ascendcorp.aml.namescreening.controller;

import com.ascendcorp.aml.namescreening.handler.FalseMatchMappingHandler;
import com.ascendcorp.aml.namescreening.handler.WhitelistHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
@RequestMapping("/internal/cache")
public class CacheManagementController {

    private final FalseMatchMappingHandler falseMatchMappingHandler;
    private final WhitelistHandler whitelistHandler;

    @PostMapping("/whitelist/init")
    public void initializeWhitelistCache() {
        whitelistHandler.initializeWhitelistCache();
    }

    @PostMapping("/false-match/init")
    public void initializeFalseMatchCache() throws InterruptedException {
        falseMatchMappingHandler.initializeFalseMatchCache();
    }
}
