package com.ascendcorp.aml.namescreening.scheduler;

import com.ascendcorp.aml.namescreening.handler.FalseMatchMappingHandler;
import com.ascendcorp.aml.namescreening.handler.WhitelistHandler;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@RequiredArgsConstructor
public class CacheManagementScheduler {

    private final FalseMatchMappingHandler falseMatchMappingHandler;
    private final WhitelistHandler whitelistHandler;

    @Scheduled(cron = "${name-screening-cache.cron-expression}")
    public void process() throws InterruptedException {
        falseMatchMappingHandler.initializeFalseMatchCache();
        whitelistHandler.initializeWhitelistCache();
    }

}
