package com.ascendcorp.aml.namescreening.handler;

import com.ascendcorp.aml.namescreening.config.es.ElasticsearchProperties;
import com.ascendcorp.aml.namescreening.entity.FalseMatchEntity;
import com.ascendcorp.aml.namescreening.entity.MerchantFalseMatchEntity;
import com.ascendcorp.aml.namescreening.repository.FalseMatchRepository;
import com.ascendcorp.aml.namescreening.repository.MerchantFalseMatchRepository;
import com.ascendcorp.aml.namescreening.request.MerchantRescoringRequest;
import com.ascendcorp.aml.namescreening.request.RescoringRequest;
import com.ascendcorp.aml.namescreening.request.UboRescoringRequest;
import jakarta.annotation.PostConstruct;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang3.StringUtils;
import org.opensearch.action.index.IndexResponse;
import org.opensearch.action.search.SearchRequest;
import org.opensearch.core.rest.RestStatus;
import org.opensearch.index.query.BoolQueryBuilder;
import org.opensearch.index.query.QueryBuilder;
import org.opensearch.index.query.QueryBuilders;
import org.opensearch.index.reindex.BulkByScrollResponse;
import org.opensearch.index.reindex.DeleteByQueryRequest;
import org.opensearch.search.builder.SearchSourceBuilder;
import org.springframework.boot.autoconfigure.cache.CacheProperties;
import org.springframework.cache.CacheManager;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.StringQuery;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.cache.caffeine.CaffeineCache;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.LinkedList;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

@Component
@Slf4j
@RequiredArgsConstructor
public class FalseMatchMappingHandler {

  private static final String INCIDENT_ID = "incident_id";
  private static final String PROFILE_ID = "profile_id";
  private static final String MERCHANT_PROFILE_ID = "merchant_profile_id";
  private static final String COUNTRY_CODE = "country_code";
  private static final int MAX_RETRIES = 3;
  private static final int RETRY_DELAY_MS = 5000;
  private volatile boolean isCacheFalseMatchInitialized = false;

  private final FalseMatchRepository falseMatchRepository;
  private final MerchantFalseMatchRepository merchantFalseMatchRepository;
  private final ElasticsearchProperties elasticsearchProperties;
  private final EsSearchQueryBuilder esSearchQueryBuilder;
  private ConcurrentHashMap<String, FalseMatchEntity> falseMatchEntityCache = new ConcurrentHashMap<>();


  @PostConstruct
  public void initializeFalseMatchCache() {
    log.info("Initializing false match cache...");
    for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        StringQuery allFalseMatchQuery = new StringQuery("{\"match_all\": {}}");
        allFalseMatchQuery.setPageable(PageRequest.of(0, elasticsearchProperties.getPageSizeCaching()));

        List<FalseMatchEntity> falseMatchEntityList = falseMatchRepository.findByScrollRequest(
                allFalseMatchQuery, elasticsearchProperties.getMappingFalseMatchIndexName(),
                elasticsearchProperties.getScrollTimeValue());

        log.info("Loaded {} falseMatch entries for caching", falseMatchEntityList.size());

        isCacheFalseMatchInitialized = false;
        falseMatchEntityCache.clear();
        log.info("Cleared existing falseMatch cache");
        for (FalseMatchEntity entity : falseMatchEntityList) {
          String cacheKey = entity.getProfileId() + ":" + entity.getCountryCode();
          falseMatchEntityCache.put(cacheKey, entity);
        }
        isCacheFalseMatchInitialized = true;
        log.info("FalseMatch cache initialization completed");
        break;
      } catch (Exception e) {
        log.warn("Cache initialization attempt {} failed", attempt, e);

        if (attempt >= MAX_RETRIES) {
          log.error("Failed to initialize cache after {} attempts", MAX_RETRIES, e);
        }

        try {
          log.info("Retrying in {} ms...", RETRY_DELAY_MS);
          Thread.sleep(RETRY_DELAY_MS);
        } catch (InterruptedException ie) {
          Thread.currentThread().interrupt();
          log.error("Retry interrupted", ie);
        }
      }
    }
  }

  public void saveToEls(RescoringRequest request) {

    QueryBuilder queryBuilder = buildQueryMatchIncidentId(request.getIncidentId());
    BulkByScrollResponse bulkByScrollResponse = falseMatchRepository
        .deleteFromEls(buildFalseMatchMappingDeleteRequest(queryBuilder,
                elasticsearchProperties.getMappingFalseMatchIndexName()));
    log.info("method: saveToEls, delete old mapping: {} documents", bulkByScrollResponse.getStatus().getDeleted());

    FalseMatchEntity falseMatchEntity = buildFalseMatchEntity(request.getCountryCode(),
        request.getIncidentId(), request.getProfileId(), request.getFalseMatchEntities());
    IndexResponse indexResponse = falseMatchRepository
        .saveToEls(falseMatchEntity, elasticsearchProperties.getMappingFalseMatchIndexName());

    if (indexResponse.status() != RestStatus.CREATED) {
      log.error("method: saveToEls, save mapping false match of profile id {} failed",
          request.getProfileId());
    } else {
      log.info("method: saveToEls, save mapping false match of profile id {} with entities [{}]",
          request.getProfileId(), falseMatchEntity.getEntities());
    }
  }

  public void saveUboFalseMatchToEls(UboRescoringRequest request) {
    QueryBuilder queryBuilder = buildQueryMatchIncidentId(request.getIncidentId());
    BulkByScrollResponse bulkByScrollResponse = falseMatchRepository
        .deleteFromEls(buildFalseMatchMappingDeleteRequest(queryBuilder,
                elasticsearchProperties.getMappingFalseMatchIndexName()));
    log.info("method: saveUboFalseMatchToEls, deleted old mapping: {} documents",
            bulkByScrollResponse.getStatus().getDeleted());

    List<FalseMatchEntity> falseMatchDocuments = new LinkedList<>();
    falseMatchDocuments.add(buildFalseMatchEntity(request.getCountryCode(),
        request.getIncidentId(), request.getProfileId(), request.getFalseMatchEntities()));
    falseMatchDocuments
        .add(buildFalseMatchEntity(request.getCountryCode(), request.getIncidentId(),
            DigestUtils.sha256Hex(request.getProfileId()), request.getFalseMatchEntities()));

    if (request.getExtraUserFalseMatch() != null && !request.getExtraUserFalseMatch().isEmpty()) {
      falseMatchDocuments.addAll(
          request.getExtraUserFalseMatch().stream()
            .map(extraUser -> buildFalseMatchEntity(request.getCountryCode(),
                request.getIncidentId(), extraUser.getExtraUserId(),
                extraUser.getFalseMatchEntities()))
            .toList()
      );
    }

    for (FalseMatchEntity falseMatchEntity : falseMatchDocuments) {
      IndexResponse indexResponse = falseMatchRepository
              .saveToEls(falseMatchEntity, elasticsearchProperties.getMappingFalseMatchIndexName());
      RestStatus responseStatus = indexResponse.status();

      if (RestStatus.CREATED.equals(responseStatus)) {
        log.error("method: saveUboFalseMatchToEls, saved mapping false match success: {}", falseMatchEntity);
      } else {
        log.info("method: saveUboFalseMatchToEls, saved mapping false match fail: {}", falseMatchEntity);
      }
    }
  }

  public void saveMerchantFalseMatchToEls(MerchantRescoringRequest request) {
    if (StringUtils.isBlank(request.getMerchantProfileId())) {
      return;
    }
    log.info("method: saveMerchantFalseMatchToEls, Save Merchant False Match to Els with merchantProfileId: {} " +
            "and incidentId: {}", request.getMerchantProfileId(), request.getIncidentId());

    QueryBuilder queryBuilder = buildQueryMatchIncidentId(request.getIncidentId());
    BulkByScrollResponse bulkByScrollResponse = merchantFalseMatchRepository
        .deleteFromEls(buildFalseMatchMappingDeleteRequest(queryBuilder,
                elasticsearchProperties.getMerchantMappingFalseMatchIndexName()));
    log.info("delete {} old mapping", bulkByScrollResponse.getStatus().getDeleted());

    List<MerchantFalseMatchEntity> merchantFalseMatchDocuments = new LinkedList<>();
    merchantFalseMatchDocuments.add(buildMerchantFalseMatchEntity(request.getCountryCode()
        ,request.getIncidentId(),request.getMerchantProfileId(),request.getFalseMatchEntities()));

    if (!CollectionUtils.isEmpty(request.getExtraUserFalseMatch())){
      merchantFalseMatchDocuments.addAll(
          request.getExtraUserFalseMatch().stream()
              .map(individual -> buildMerchantFalseMatchEntity(request.getCountryCode(),
                  request.getIncidentId(), individual.getExtraUserId(),
                  individual.getFalseMatchEntities())
          ).toList()
      );
    }

    for (MerchantFalseMatchEntity merchantFalseMatchEntity : merchantFalseMatchDocuments) {
      IndexResponse indexResponse = merchantFalseMatchRepository
              .saveToEls(merchantFalseMatchEntity, elasticsearchProperties.getMerchantMappingFalseMatchIndexName());
      if (indexResponse.status() != RestStatus.CREATED) {
        log.error("method: saveMerchantFalseMatchToEls, save mapping false match of merchant profile id {}, " +
                "incidentId {} failed", request.getMerchantProfileId(), request.getIncidentId());
      } else {
        log.info("method: saveMerchantFalseMatchToEls, save mapping false match of merchant profile id {}, " +
                "incidentId {} success", request.getMerchantProfileId(),request.getIncidentId());
      }
    }
  }

  private DeleteByQueryRequest buildFalseMatchMappingDeleteRequest(QueryBuilder queryBuilder,
      String indexName) {

    DeleteByQueryRequest deleteRequest = new DeleteByQueryRequest(indexName);
    deleteRequest.setQuery(queryBuilder);

    return deleteRequest;
  }

  private QueryBuilder buildQueryMatchIncidentId(String incidentId) {
    return QueryBuilders.matchQuery(INCIDENT_ID, incidentId);
  }

  public List<String> getListFalseMatchEntityIds(String profileId, String countryCode) {
    log.info("method: getListFalseMatchEntityIds, profileId: {}, countryCode: {}",
        profileId, countryCode);
    if (StringUtils.isBlank(profileId) || StringUtils.isBlank(countryCode)) {
      return Collections.emptyList();
    }

    Set<String> entityIds = new HashSet<>();
    if (isCacheFalseMatchInitialized) {
      String cacheKey = profileId + ":" + countryCode;
      FalseMatchEntity cachedEntity = falseMatchEntityCache.get(cacheKey);
      if (cachedEntity != null) {
        log.info("method: getListFalseMatchEntityIds, cache hit for profileId: {}", profileId);
        entityIds = new HashSet<>(Arrays.asList(cachedEntity.getEntities().split(",")));
      }
    } else {
      log.info("method: getListFalseMatchEntityIds, cache is not initialized, start get from els");
      entityIds = getListFalseMatchEntityFromEls(profileId, countryCode);
    }

    log.info("method: getListFalseMatchEntityIds, profileId: {}, entityIds: {}", profileId,
            entityIds);
    return new ArrayList<>(entityIds);
  }

  private Set<String> getListFalseMatchEntityFromEls(String profileId, String countryCode) {
    StringQuery strQuery = esSearchQueryBuilder.buildFalseMatchSearchRequest(PROFILE_ID, profileId,
            countryCode);
    List<FalseMatchEntity> falseMatchEntities = falseMatchRepository
            .findBySearchRequest(strQuery, elasticsearchProperties.getMappingFalseMatchIndexName());
    Set<String> entityIds = new HashSet<>();
    falseMatchEntities.forEach(entity ->
            entityIds.addAll(Arrays.asList(entity.getEntities().split(","))));

    return entityIds;
  }

  public List<FalseMatchEntity> getListFalseMatchEntityIds(Collection<String> profileIds, String countryCode) {
    log.info("method: getListFalseMatchEntityIds, profileId: {}, countryCode: {}",
        profileIds, countryCode);

    return falseMatchRepository
        .findBySearchRequest(buildSearchRequestForFalseMatchEntity(PROFILE_ID, profileIds,
            countryCode, elasticsearchProperties.getMappingFalseMatchIndexName()));
  }

  public List<String> getListMerchantFalseMatchEntityIds(String merchantProfileId,
      String countryCode) {
    log.info("method: getListMerchantFalseMatchEntityIds, merchantProfileId: {}, countryCode: {}",
        merchantProfileId, countryCode);
    if (StringUtils.isBlank(merchantProfileId) || StringUtils.isBlank(countryCode)) {
      return Collections.emptyList();
    }

    List<MerchantFalseMatchEntity> falseMatchEntities = getListMerchantFalseMatchEntity(merchantProfileId,countryCode);
    Set<String> entityIds = new HashSet<>();
    falseMatchEntities.forEach(entity ->
        entityIds.addAll(Arrays.asList(entity.getEntities().split(","))));
    log.info("method: getListMerchantFalseMatchEntityIds, merchantProfileId: {}, entityIds: {}",
        merchantProfileId, entityIds);
    return new ArrayList<>(entityIds);
  }

  public List<MerchantFalseMatchEntity> getListMerchantFalseMatchEntity(Collection<String> merchantProfileIds, String countryCode){
    return merchantFalseMatchRepository.findBySearchRequest(
        buildSearchRequestForFalseMatchEntity(MERCHANT_PROFILE_ID, merchantProfileIds, countryCode,
            elasticsearchProperties.getMerchantMappingFalseMatchIndexName()));
  }

  public List<MerchantFalseMatchEntity> getListMerchantFalseMatchEntity(String merchantProfileId,
      String countryCode){
    return merchantFalseMatchRepository.findBySearchRequest(
        buildSearchRequestForFalseMatchEntity(MERCHANT_PROFILE_ID, merchantProfileId, countryCode,
            elasticsearchProperties.getMerchantMappingFalseMatchIndexName()));
  }

  public List<String> getListFalseMatchEntityIds(String incidentId) {
    log.info("method: getListFalseMatchEntityIds, incidentId: {}",
        incidentId);
    QueryBuilder queryBuilder = buildQueryMatchIncidentId(incidentId);
    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.query(queryBuilder);

    SearchRequest searchRequest = new SearchRequest(elasticsearchProperties.getMappingFalseMatchIndexName());
    searchRequest.source(searchSourceBuilder);

    List<FalseMatchEntity> falseMatchEntities =
        falseMatchRepository.findBySearchRequest(searchRequest);
    Set<String> entityIds = new HashSet<>();
    falseMatchEntities.forEach(entity ->
        entityIds.addAll(Arrays.asList(entity.getEntities().split(","))));
    log.debug("method: getListFalseMatchEntityIds, incidentId: {}, entityIds: {}", incidentId,
        entityIds);

    return new ArrayList<>(entityIds);
  }

  public List<String> getListMerchantFalseMatchEntityIds(String incidentId) {
    log.info("method: getListMerchantFalseMatchEntityIds, incidentId: {}",
        incidentId);
    QueryBuilder queryBuilder = buildQueryMatchIncidentId(incidentId);
    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.query(queryBuilder);
    SearchRequest searchRequest = new SearchRequest(elasticsearchProperties.getMappingFalseMatchIndexName());
    searchRequest.source(searchSourceBuilder);
    List<MerchantFalseMatchEntity> falseMatchEntities =
        merchantFalseMatchRepository.findBySearchRequest(searchRequest);
    Set<String> entityIds = new HashSet<>();
    falseMatchEntities.forEach(entity ->
        entityIds.addAll(Arrays.asList(entity.getEntities().split(","))));
    log.debug("method: getListMerchantFalseMatchEntityIds, incidentId: {}, entityIds: {}", incidentId, entityIds);
    return new ArrayList<>(entityIds);
  }

  private SearchRequest buildSearchRequestForFalseMatchEntity(String profileIdType, Collection<String> profileIds, String countryCode, String indexName){
    BoolQueryBuilder profilesQuery = QueryBuilders.boolQuery();
    profileIds.forEach(profileId -> {
      BoolQueryBuilder profileQuery = QueryBuilders.boolQuery()
          .must(QueryBuilders.matchQuery(profileIdType, profileId))
          .must(QueryBuilders.matchQuery(COUNTRY_CODE, countryCode));
      profilesQuery.should(profileQuery);
    });

    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.query(profilesQuery);
    searchSourceBuilder.size(elasticsearchProperties.getMaxSize());

    SearchRequest searchRequest = new SearchRequest(indexName);
    searchRequest.source(searchSourceBuilder);

    return searchRequest;
  }

  private SearchRequest buildSearchRequestForFalseMatchEntity(String profileIdType, String profileId, String countryCode, String indexName){
    BoolQueryBuilder profileQuery = QueryBuilders.boolQuery()
        .must(QueryBuilders.matchQuery(profileIdType, profileId))
        .must(QueryBuilders.matchQuery(COUNTRY_CODE, countryCode));

    SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
    searchSourceBuilder.query(profileQuery);
    searchSourceBuilder.size(elasticsearchProperties.getMaxSize());

    SearchRequest searchRequest = new SearchRequest(indexName);
    searchRequest.source(searchSourceBuilder);

    return searchRequest;
  }

  private MerchantFalseMatchEntity buildMerchantFalseMatchEntity(String countryCode, String incidentId,
      String merchantProfileId, List<String> entities){
    MerchantFalseMatchEntity merchantFalseMatchEntity = new MerchantFalseMatchEntity();
    merchantFalseMatchEntity.setCountryCode(countryCode);
    merchantFalseMatchEntity.setIncidentId(incidentId);
    merchantFalseMatchEntity.setMerchantProfileId(merchantProfileId);
    merchantFalseMatchEntity.setEntities(String.join(",", entities));
    return merchantFalseMatchEntity;
  }


  private FalseMatchEntity buildFalseMatchEntity(String countryCode, String incidentId,
      String profileId, List<String> entities) {
    FalseMatchEntity falseMatchEntity = new FalseMatchEntity();
    falseMatchEntity.setCountryCode(countryCode);
    falseMatchEntity.setIncidentId(incidentId);
    falseMatchEntity.setProfileId(profileId);
    falseMatchEntity.setEntities(String.join(",", entities));
    return falseMatchEntity;
  }

}
