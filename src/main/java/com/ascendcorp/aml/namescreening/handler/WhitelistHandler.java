package com.ascendcorp.aml.namescreening.handler;

import com.ascendcorp.aml.namescreening.config.es.ElasticsearchProperties;
import com.ascendcorp.aml.namescreening.constant.NameScreeningEventType;
import com.ascendcorp.aml.namescreening.constant.ScreeningConfigDetailExtraType;
import com.ascendcorp.aml.namescreening.dto.AttachProfileDto;
import com.ascendcorp.aml.namescreening.dto.FieldMappingDto;
import com.ascendcorp.aml.namescreening.dto.ScreeningConfigDto;
import com.ascendcorp.aml.namescreening.dto.ScreeningDetailDto;
import com.ascendcorp.aml.namescreening.entity.WhitelistEntity;
import com.ascendcorp.aml.namescreening.repository.WhitelistRepository;
import com.ascendcorp.aml.namescreening.request.NameScreeningRequest;
import com.ascendcorp.aml.namescreening.request.UboScreeningRequest;
import com.ascendcorp.aml.namescreening.service.NameScreeningConfigService;
import com.ascendcorp.aml.namescreening.util.StringUtil;
import jakarta.annotation.PostConstruct;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.beanutils.BeanUtils;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.query.StringQuery;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@RequiredArgsConstructor
public class WhitelistHandler {

  private static final int MAX_RETRIES = 3;
  private static final int RETRY_DELAY_MS = 5000;
  private static final String DELIMITER = "&&";
  private static final int DEFAULT_TOTAL_HIT = 1;
  private static final int DEFAULT_NOT_HIT = 0;
  private final ConcurrentHashMap<String, WhitelistEntity> whitelistCache = new ConcurrentHashMap<>();
  private volatile boolean isCacheWhitelistInitialized = false;

  private final NameScreeningConfigService nameScreeningConfigService;
  private final WhitelistRepository whitelistRepository;
  private final EsSearchQueryBuilder esSearchQueryBuilder;
  private final ElasticsearchProperties elasticsearchProperties;

  @PostConstruct
  public void initializeWhitelistCache() {
    log.info("Initializing white list cache...");
    for (int attempt = 1; attempt <= MAX_RETRIES; attempt++) {
      try {
        StringQuery allWhiteListQuery = new StringQuery("{\"match_all\": {}}");
        allWhiteListQuery.setPageable(PageRequest.of(0, elasticsearchProperties.getPageSizeCaching()));

        List<WhitelistEntity> whitelistEntities = whitelistRepository.findByScrollRequest(
                allWhiteListQuery, elasticsearchProperties.getWhitelistIndexName(),
                elasticsearchProperties.getScrollTimeValue());

        log.info("Loaded {} whiteList entries for caching", whitelistEntities.size());

        List<ScreeningConfigDto> whitelistConfigList = nameScreeningConfigService
                .getAllScreeningConfigurationByEventType(NameScreeningEventType.WHITELIST.name());
        Map<String, ScreeningConfigDto> whitelistConfigMap = whitelistConfigList.stream()
                .collect(Collectors.toMap(ScreeningConfigDto::getCountryCode, config -> config));
        isCacheWhitelistInitialized = false;
        whitelistCache.clear();
        log.info("Cleared existing whiteList cache");
        for (WhitelistEntity entity : whitelistEntities) {
          ScreeningConfigDto whitelistConfig = whitelistConfigMap.get(entity.getCountryCode());
          processEntityWithConfig(entity, whitelistConfig);
        }
        isCacheWhitelistInitialized = true;
        log.info("WhiteList cache initialization completed");
        break;
      } catch (Exception e) {
        log.warn("Cache initialization attempt {} failed", attempt, e);

        if (attempt >= MAX_RETRIES) {
          log.error("Failed to initialize cache after {} attempts", MAX_RETRIES, e);
        }

        try {
          log.info("Retrying in {} ms...", RETRY_DELAY_MS);
          Thread.sleep(RETRY_DELAY_MS);
        } catch (InterruptedException ie) {
          Thread.currentThread().interrupt();
          log.error("Retry interrupted", ie);
        }
      }
    }
  }

  public void checkIfUboInWhitelist(UboScreeningRequest request) {
    checkIfIsInWhitelist(request);
    request.getAttachProfiles()
        .forEach(attachProfile -> checkIfIsInWhitelist(attachProfile, request.getCountryCode()));
  }

  public boolean checkIfIsInWhitelist(NameScreeningRequest request) {
    List<List<FieldMappingDto>> fieldMappingList = nameScreeningConfigService.getFieldMapping(
        request.getCountryCode(), NameScreeningEventType.WHITELIST.name(),
        ScreeningConfigDetailExtraType.DEFAULT.getValue());

    int totalHit = 0;
    if (isCacheWhitelistInitialized) {
      totalHit = checkIfIsInWhitelistWithCache(request, fieldMappingList);
    } else {
      StringQuery strQuery = esSearchQueryBuilder.buildWhitelistSearchQuery(request,
              fieldMappingList);
      if (strQuery == null) {
        return false;
      }
      totalHit = whitelistRepository.totalHitBySearchRequest(strQuery,
              elasticsearchProperties.getWhitelistIndexName());
    }

    log.info(
        "Query whitelist [NationalID: {}, fullname: {}, firstname: {}, middle-name: {}, lastname: {}] | Found {}",
        request.getNationalId(), request.getFullName(), request.getFirstName(),
        request.getMiddleName(), request.getLastName(), totalHit);
    request.setInWhitelist(totalHit > 0);

    return request.isInWhitelist();
  }

  private int checkIfIsInWhitelistWithCache(NameScreeningRequest request, List<List<FieldMappingDto>> fieldMappingList) {

    for (List<FieldMappingDto> fieldMapping : fieldMappingList) {
      String whitelistKey = buildWhitelistKey(request, fieldMapping, request.getCountryCode(), false);
      if (whitelistCache.containsKey(whitelistKey)) {
        return DEFAULT_TOTAL_HIT;
      }
    }
    return DEFAULT_NOT_HIT;
  }

  public void checkIfIsInWhitelist(AttachProfileDto request, String countryCode) {
    List<List<FieldMappingDto>> fieldMappingList = nameScreeningConfigService.getFieldMapping(
        countryCode, NameScreeningEventType.WHITELIST.name(),
        ScreeningConfigDetailExtraType.DEFAULT.getValue());

    StringQuery query = esSearchQueryBuilder.buildExtraUserWhitelistSearchQuery(
        request, fieldMappingList, countryCode);

    int totalHit = whitelistRepository.totalHitBySearchRequest(query,
        elasticsearchProperties.getWhitelistIndexName());
    log.info(
        "Query whitelist [NationalID: {}, fullname: {}, firstname: {}, middle-name: {}, lastname: {}] | Found {}",
        request.getNationalId(), request.getFullName(), request.getFirstName(),
        request.getMiddleName(), request.getLastName(), totalHit);
    request.setInWhitelist(totalHit > 0);
  }

  private static String getFieldValueByScreeningConfig(Object object, String fieldName) {
    String value = null;
    try {

      value = BeanUtils.getProperty(object, fieldName);
    } catch (Exception e) {
      log.error("method: getFieldValueByScreeningConfig, fieldName: {}, error: {}", fieldName,
              e.getMessage());
    }
    return value;
  }

  private void processEntityWithConfig(WhitelistEntity entity, ScreeningConfigDto whitelistConfig) {
    for (ScreeningDetailDto screeningDetail : whitelistConfig.getConfigs()) {
      String whitelistKey = buildWhitelistKey(entity, screeningDetail.getFieldMappings(), entity.getCountryCode(), true);
      whitelistCache.put(whitelistKey, entity);
    }
  }

  private String buildWhitelistKey(Object entity, List<FieldMappingDto> fieldMappingDtoList,
                                                      String countryCode, boolean isSourceField) {
    StringBuilder whitelistKeyBuilder = new StringBuilder();
    whitelistKeyBuilder.append(countryCode).append(DELIMITER);

    for (FieldMappingDto fieldMapping : fieldMappingDtoList) {
      String fieldName;
      if (isSourceField) {
        fieldName = StringUtil.snakeToCamel(fieldMapping.getSourceField());
      } else {
        fieldName = fieldMapping.getRequestField();
      }
      String value = getFieldValueByScreeningConfig(entity, fieldName);
      whitelistKeyBuilder.append(value).append(DELIMITER);
    }

    // Remove the last delimiter
    if (whitelistKeyBuilder.length() >= DELIMITER.length()) {
      whitelistKeyBuilder.setLength(whitelistKeyBuilder.length() - DELIMITER.length());
    }

    return whitelistKeyBuilder.toString();
  }

}
