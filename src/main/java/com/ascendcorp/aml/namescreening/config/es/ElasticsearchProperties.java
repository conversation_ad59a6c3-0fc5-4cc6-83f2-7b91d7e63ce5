package com.ascendcorp.aml.namescreening.config.es;

import com.ascendcorp.aml.namescreening.config.ApplicationProperties;
import com.ascendcorp.aml.namescreening.constant.EsSearchConstants;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.Map;

@ConfigurationProperties(prefix = "elastic")
@Data
@RefreshScope
public class ElasticsearchProperties {

  private String hosts;

  private String scheme;

  private int maxSize = 10;

  private int maxSizeOfScoring = 20;

  private Map<String, String> queryTemplate;

  private long scrollTimeValue = 60000L;

  private int pageSizeCaching = 5000;


  private ApplicationProperties applicationProperties;

  @Autowired
  public ElasticsearchProperties(ApplicationProperties applicationProperties) {
    this.applicationProperties = applicationProperties;
  }

  public String getRiskSeverityIndexName() {
    return EsSearchConstants.RISK_SEVERITY_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

  public String getMerchantRiskSeverityIndexName() {
    return EsSearchConstants.MERCHANT_SEVERITY_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

  public String getWhitelistIndexName() {
    return EsSearchConstants.WHITELIST_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

  public String getMerchantWhitelistIndexName() {
    return EsSearchConstants.MERCHANT_WHITELIST_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

  public String getMappingFalseMatchIndexName() {
    return EsSearchConstants.MAPPING_FALSE_MATCH_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

  public String getMerchantMappingFalseMatchIndexName() {
    return EsSearchConstants.MERCHANT_MAPPING_FALSE_MATCH_INDEX_PREFIX + applicationProperties.getEnvironment().toLowerCase();
  }

}
